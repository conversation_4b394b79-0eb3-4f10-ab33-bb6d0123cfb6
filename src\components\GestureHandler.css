/* GestureHandler Component Styles */
.gesture-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  touch-action: none; /* Prevent default touch behaviors */
  user-select: none; /* Prevent text selection during gestures */
}

/* Debug Overlay */
.gesture-debug {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  z-index: 2000;
  min-width: 300px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.debug-header button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.debug-header button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.debug-content {
  padding: 20px;
}

.debug-content p {
  margin-bottom: 15px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 6px;
  word-break: break-all;
}

.debug-help h4 {
  margin-bottom: 10px;
  color: #fff;
  font-size: 1rem;
}

.debug-help ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.debug-help li {
  padding: 6px 0;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-help li::before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  font-size: 1.2rem;
}

/* Touch Feedback */
.gesture-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  background: transparent;
  transition: background 0.1s ease;
}

.gesture-container.touching::after {
  background: rgba(102, 126, 234, 0.05);
}

/* Gesture Visual Feedback */
.gesture-feedback {
  position: fixed;
  pointer-events: none;
  z-index: 1500;
  background: rgba(102, 126, 234, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  transform: translate(-50%, -50%);
  animation: gesturePopup 0.6s ease-out forwards;
}

@keyframes gesturePopup {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1) translateY(-20px);
  }
}

/* Swipe Direction Indicators */
.swipe-indicator {
  position: fixed;
  pointer-events: none;
  z-index: 1400;
  font-size: 2rem;
  color: rgba(102, 126, 234, 0.8);
  animation: swipeIndicator 0.5s ease-out forwards;
}

.swipe-indicator.up {
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
}

.swipe-indicator.down {
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
}

.swipe-indicator.left {
  top: 50%;
  left: 20%;
  transform: translateY(-50%);
}

.swipe-indicator.right {
  top: 50%;
  right: 20%;
  transform: translateY(-50%);
}

@keyframes swipeIndicator {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}

/* Gesture Trail Effect */
.gesture-trail {
  position: fixed;
  pointer-events: none;
  z-index: 1300;
  width: 4px;
  height: 4px;
  background: rgba(102, 126, 234, 0.6);
  border-radius: 50%;
  animation: trailFade 0.8s ease-out forwards;
}

@keyframes trailFade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.2);
  }
}

/* Complex Gesture Recognition Visual */
.gesture-path {
  position: fixed;
  pointer-events: none;
  z-index: 1200;
  stroke: rgba(102, 126, 234, 0.6);
  stroke-width: 3;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  animation: pathDraw 0.5s ease-out;
}

@keyframes pathDraw {
  0% {
    stroke-dasharray: 0 1000;
    opacity: 1;
  }
  100% {
    stroke-dasharray: 1000 0;
    opacity: 0;
  }
}

/* Keyboard Shortcuts Overlay */
.keyboard-shortcuts {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
  z-index: 100;
  opacity: 0;
  animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

/* Animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .gesture-debug {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
  
  .debug-header,
  .debug-content {
    padding: 15px;
  }
  
  .gesture-feedback {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
  
  .swipe-indicator {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .gesture-debug {
    top: 5px;
    right: 5px;
    left: 5px;
  }
  
  .debug-header,
  .debug-content {
    padding: 12px;
  }
  
  .debug-help li {
    font-size: 0.8rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .gesture-debug {
    background: black;
    border: 2px solid white;
  }
  
  .gesture-feedback {
    background: black;
    border: 2px solid white;
  }
  
  .swipe-indicator {
    color: white;
    text-shadow: 2px 2px 4px black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .gesture-debug,
  .gesture-feedback,
  .swipe-indicator,
  .gesture-trail {
    animation: none;
  }
  
  .gesture-container::after {
    transition: none;
  }
}
