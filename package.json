{"name": "knowledge-tree-explorer", "version": "1.0.0", "description": "Interactive Knowledge Tree Web App with Gesture Navigation and OpenRouter API Integration", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx", "lint:fix": "eslint src --ext .js,.jsx --fix", "format": "prettier --write src/**/*.{js,jsx,css}", "analyze": "npm run build && npx serve -s build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-unused-vars": "warn", "no-console": "off", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.23.0", "prettier": "^2.7.1"}, "keywords": ["knowledge-tree", "interactive-learning", "gesture-navigation", "openrouter", "ai-powered", "education", "react"], "author": "Knowledge Tree Explorer Team", "license": "MIT", "homepage": ".", "proxy": "http://localhost:3001"}