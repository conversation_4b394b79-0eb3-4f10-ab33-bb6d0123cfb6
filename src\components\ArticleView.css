/* ArticleView Component Styles */
.article-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
}

/* Reading Progress Bar */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(102, 126, 234, 0.1);
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

/* Article Header */
.article-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.article-actions {
  display: flex;
  gap: 10px;
}

.tts-btn, .save-btn, .back-btn {
  background: rgba(102, 126, 234, 0.1);
  border: 2px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.tts-btn:hover, .save-btn:hover, .back-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tts-btn.active {
  background: #667eea;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Breadcrumbs */
.breadcrumbs {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  font-size: 0.9rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.breadcrumb-link {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
  font-size: 0.9rem;
}

.breadcrumb-link:hover {
  background: rgba(102, 126, 234, 0.1);
}

.separator {
  color: #999;
  margin: 0 2px;
}

/* Article Content */
.article-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 100px);
  animation: slideUp 0.5s ease-out;
}

.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.article-position {
  margin-bottom: 30px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.1);
  border-left: 4px solid #667eea;
  border-radius: 0 8px 8px 0;
}

.position-text {
  color: #667eea;
  font-weight: 600;
  font-size: 0.95rem;
}

/* Article Body */
.article-body {
  line-height: 1.8;
  font-size: 1.1rem;
  color: #444;
  margin-bottom: 40px;
}

.content-heading-1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 40px 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.content-heading-2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #444;
  margin: 30px 0 15px 0;
}

.content-heading-3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #555;
  margin: 25px 0 12px 0;
}

.content-paragraph {
  margin-bottom: 20px;
  text-align: justify;
}

/* Related Topics */
.related-topics {
  margin: 40px 0;
  padding: 25px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.related-topics h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.topic-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.topic-tag:hover {
  transform: translateY(-2px);
}

/* Article Flags */
.article-flags {
  margin: 30px 0;
  padding: 20px;
  background: rgba(118, 75, 162, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(118, 75, 162, 0.1);
}

.article-flags h4 {
  color: #333;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.flags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.flag-badge {
  background: rgba(118, 75, 162, 0.1);
  color: #764ba2;
  padding: 6px 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  font-weight: bold;
  border: 1px solid rgba(118, 75, 162, 0.2);
}

/* Gesture Hints */
.gesture-hints {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
  z-index: 50;
}

.hint {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gesture {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: bold;
  min-width: 24px;
  text-align: center;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-left: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #667eea;
  font-size: 1.2rem;
  text-align: center;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-header {
    padding: 15px;
    flex-direction: column;
    align-items: stretch;
  }
  
  .article-actions {
    justify-content: center;
  }
  
  .article-content {
    padding: 30px 15px;
    border-radius: 15px 15px 0 0;
  }
  
  .article-title {
    font-size: 2rem;
  }
  
  .content-heading-1 {
    font-size: 1.6rem;
  }
  
  .content-heading-2 {
    font-size: 1.3rem;
  }
  
  .gesture-hints {
    position: relative;
    bottom: auto;
    left: auto;
    margin: 20px;
  }
}

@media (max-width: 480px) {
  .article-content {
    padding: 25px 12px;
  }
  
  .article-title {
    font-size: 1.8rem;
  }
  
  .article-body {
    font-size: 1rem;
  }
  
  .related-topics,
  .article-flags {
    padding: 20px 15px;
  }
}
