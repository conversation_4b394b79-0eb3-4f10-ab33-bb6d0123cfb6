// API integration with R1
export async function generateKnowledgeTree(topic) {
  try {
    // Replace with actual API endpoint
    const response = await fetch('/api/generate-tree', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ topic }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate knowledge tree');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error generating knowledge tree:', error);
    throw error;
  }
}

export async function generateArticle(topic, branch, flags = ['-a']) {
  try {
    // Replace with actual API endpoint
    const response = await fetch('/api/generate-article', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ topic, branch, flags }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate article');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error generating article:', error);
    throw error;
  }
}