// OpenRouter API Service for Knowledge Tree Generation
const OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || 'your-api-key-here';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const MODEL = 'deepseek/deepseek-r1-0528:free';

// Site configuration for OpenRouter rankings
const SITE_CONFIG = {
  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',
  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'
};

class OpenRouterClient {
  constructor() {
    this.baseURL = OPENROUTER_BASE_URL;
    this.apiKey = OPENROUTER_API_KEY;
  }

  async makeRequest(messages, temperature = 0.7) {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          ...SITE_CONFIG
        },
        body: JSON.stringify({
          model: MODEL,
          messages,
          temperature,
          max_tokens: 4000,
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('OpenRouter API request failed:', error);
      throw error;
    }
  }
}

const client = new OpenRouterClient();

// Generate Knowledge Tree from topic
export async function generateKnowledgeTree(topic) {
  const prompt = `Generate a comprehensive knowledge tree for the topic: "${topic}"

Return ONLY a valid JSON object with this exact structure:
{
  "tema": "${topic}",
  "ramuri": [
    {
      "nume": "Branch Name",
      "descriere": "Brief description of this branch",
      "emoji": "🔬",
      "subcategorii": ["sub1", "sub2", "sub3"]
    }
  ]
}

Requirements:
- Generate 8-12 main branches that cover the topic comprehensively
- Each branch should have a relevant emoji
- Include 3-5 subcategories for each branch
- Descriptions should be 1-2 sentences max
- Focus on creating a logical learning progression
- Make it suitable for interactive exploration

Topic: ${topic}`;

  try {
    const response = await client.makeRequest([
      {
        role: 'system',
        content: 'You are an expert knowledge organizer. Generate comprehensive, well-structured knowledge trees in valid JSON format only. Do not include any explanatory text outside the JSON.'
      },
      {
        role: 'user',
        content: prompt
      }
    ]);

    // Parse and validate the JSON response
    const cleanResponse = response.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    const tree = JSON.parse(jsonMatch[0]);
    
    // Validate structure
    if (!tree.tema || !Array.isArray(tree.ramuri)) {
      throw new Error('Invalid tree structure');
    }

    return tree;
  } catch (error) {
    console.error('Error generating knowledge tree:', error);
    
    // Fallback tree structure
    return {
      tema: topic,
      ramuri: [
        {
          nume: "Fundamentals",
          descriere: `Basic concepts and principles of ${topic}`,
          emoji: "📚",
          subcategorii: ["Core Concepts", "Key Principles", "Basic Theory"]
        },
        {
          nume: "Applications",
          descriere: `Practical applications and use cases of ${topic}`,
          emoji: "🔧",
          subcategorii: ["Real-world Uses", "Industry Applications", "Case Studies"]
        },
        {
          nume: "Advanced Topics",
          descriere: `Complex and specialized aspects of ${topic}`,
          emoji: "🎓",
          subcategorii: ["Expert Level", "Research Areas", "Cutting Edge"]
        }
      ]
    };
  }
}

// Generate Article for specific branch
export async function generateArticle(topic, branch, flags = ['-a']) {
  const flagInstructions = {
    // Basic flags
    '-a': 'Write a standard informative article (400-600 words)',
    '-t': 'Format the content as tables and structured data',
    '-ex': 'Include 3 practical examples with detailed explanations',
    '-p': 'Include code examples and technical demonstrations',
    '-q': 'End with a 5-question quiz to test understanding',
    '-rap': 'Write an exhaustive report with comprehensive coverage',
    '-def': 'Focus on expert-level definitions and technical terminology',

    // Learning & Visualization flags
    '-path': 'Create personalized learning paths with step-by-step progression and milestones',
    '-vis': 'Generate infographics, diagrams, and interactive visualizations with detailed descriptions',
    '-vid': 'Suggest relevant videos and create video scripts with timestamps and key points',
    '-mind': 'Present information as an interactive mind map with connected concepts',
    '-flow': 'Create flowcharts and process diagrams with decision points and outcomes',

    // Industry-specific flags
    '-case': 'Include real case studies with measurable results and detailed analysis',
    '-scenario': 'Create practical scenarios and simulations with step-by-step solutions',
    '-lab': 'Design experiments and practical tests with materials and procedures',
    '-mentor': 'Include expert advice and mentorship tips from industry professionals',
    '-mistakes': 'Analyze common mistakes and provide prevention strategies',

    // Interactive tools flags
    '-calc': 'Include calculators and interactive tools for calculations with formulas',
    '-template': 'Provide actionable templates and checklists ready for immediate use',
    '-workshop': 'Format as workshop content with practical exercises and group activities',
    '-game': 'Add gamification elements with points, achievements, and competitions',
    '-team': 'Optimize content for teams and collaboration with group exercises',

    // Sharing & presentation flags
    '-share': 'Format for easy sharing with colleagues including summary points',
    '-present': 'Generate PowerPoint-style presentation with slides and talking points',
    '-meeting': 'Create meeting agenda and discussion points with time allocations',
    '-offline': 'Ensure content is available offline with downloadable resources',

    // Analytics & benchmarking flags
    '-kpi': 'Include relevant KPIs and measurable metrics with tracking methods',
    '-benchmark': 'Compare with industry best practices and competitive analysis',
    '-timeline': 'Add temporal planning and milestones with realistic deadlines',

    // Localization flags
    '-ro': 'Adapt for Romanian market and legislation with local examples',
    '-eu': 'Focus on EU regulations and practices with compliance guidelines',
    '-local': 'Include local examples and regional practices',

    // Advanced features flags
    '-auto': 'Focus on process automation with tools and implementation steps',
    '-predict': 'Include data-based predictions and future trends analysis',
    '-optimize': 'Provide continuous optimization suggestions with improvement metrics'
  };

  const activeFlags = flags.filter(flag => flagInstructions[flag]);
  const flagText = activeFlags.map(flag => flagInstructions[flag]).join('. ');

  // Special handling for Romanian and localization flags
  const hasRomanianFlag = flags.includes('-ro');
  const hasEUFlag = flags.includes('-eu');
  const hasLocalFlag = flags.includes('-local');

  let localizationContext = '';
  if (hasRomanianFlag) {
    localizationContext = 'Adaptează conținutul pentru piața românească, include legislația locală, exemple de companii românești și practici specifice României.';
  } else if (hasEUFlag) {
    localizationContext = 'Focus on European Union regulations, GDPR compliance, EU market practices, and European case studies.';
  } else if (hasLocalFlag) {
    localizationContext = 'Include local examples, regional practices, and area-specific considerations.';
  }

  // Special handling for advanced features
  const hasAdvancedFlags = flags.some(flag => ['-auto', '-predict', '-optimize'].includes(flag));
  let advancedContext = '';
  if (hasAdvancedFlags) {
    advancedContext = 'Include specific tools, software recommendations, implementation steps, and measurable outcomes.';
  }

  const prompt = `Generate an article about "${branch.nume}" in the context of "${topic}".

Article Requirements:
${flagText || flagInstructions['-a']}

${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}
${advancedContext ? `Advanced Features: ${advancedContext}` : ''}

Content Guidelines:
- Write in an engaging, educational style suitable for professionals
- Use clear headings and structure with markdown formatting
- Include relevant examples and explanations
- Make it suitable for interactive learning
- Target length: 400-600 words (unless -rap flag is used, then 800-1200 words)
- If visualization flags are used, describe diagrams and charts in detail
- If interactive flags are used, provide step-by-step instructions
- If Romanian flag is used, write in Romanian language

Return ONLY a valid JSON object with this structure:
{
  "titlu": "Article Title",
  "continut": "Full article content with proper markdown formatting",
  "subcategorii": ["related topic 1", "related topic 2", "related topic 3"],
  "flags": ${JSON.stringify(flags)},
  "pozitie": "${topic} → ${branch.nume}",
  "estimatedReadTime": "X minutes",
  "difficulty": "Beginner|Intermediate|Advanced",
  "practicalValue": "High|Medium|Low"
}

Topic Context: ${topic}
Branch: ${branch.nume}
Branch Description: ${branch.descriere}
Subcategories: ${branch.subcategorii?.join(', ') || 'None'}
Selected Flags: ${flags.join(' ')}`;

  try {
    const response = await client.makeRequest([
      {
        role: 'system',
        content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'
      },
      {
        role: 'user',
        content: prompt
      }
    ], 0.8);

    // Parse and validate the JSON response
    const cleanResponse = response.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    const article = JSON.parse(jsonMatch[0]);
    
    // Validate structure
    if (!article.titlu || !article.continut) {
      throw new Error('Invalid article structure');
    }

    return article;
  } catch (error) {
    console.error('Error generating article:', error);
    
    // Fallback article
    return {
      titlu: `${branch.nume} - ${topic}`,
      continut: `# ${branch.nume}

This section explores ${branch.nume} in the context of ${topic}.

## Overview
${branch.descriere}

## Key Concepts
Understanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.

## Applications
The concepts in ${branch.nume} have wide-ranging applications across various domains and industries.

## Further Learning
To deepen your understanding, consider exploring related topics and practical exercises.`,
      subcategorii: branch.subcategorii || [],
      flags: flags,
      pozitie: `${topic} → ${branch.nume}`
    };
  }
}

// Test API connection
export async function testConnection() {
  try {
    const response = await client.makeRequest([
      {
        role: 'user',
        content: 'Hello, please respond with "API connection successful"'
      }
    ]);
    
    return response.includes('successful');
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
}
