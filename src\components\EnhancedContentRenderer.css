/* Enhanced Content Renderer Styles */
.enhanced-content-renderer {
  width: 100%;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Content Tabs */
.content-tabs {
  display: flex;
  background: rgba(102, 126, 234, 0.05);
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.content-tabs::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: #667eea;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: rgba(102, 126, 234, 0.1);
}

.tab-button.active {
  background: white;
  color: #333;
  border-bottom-color: #667eea;
}

.tab-icon {
  font-size: 1.1rem;
}

/* Content Area */
.content-area {
  padding: 25px;
  min-height: 400px;
}

/* Visualizations */
.visualizations-container h3 {
  margin-bottom: 20px;
  color: #333;
}

.viz-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.viz-item {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.viz-item h4 {
  margin-bottom: 15px;
  color: #333;
}

.viz-placeholder {
  text-align: center;
  padding: 30px;
  background: white;
  border-radius: 8px;
  border: 2px dashed rgba(102, 126, 234, 0.3);
}

.viz-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 10px;
  transition: transform 0.2s ease;
}

.viz-button:hover {
  transform: translateY(-2px);
}

/* Video Suggestions */
.video-suggestions h3 {
  margin-bottom: 20px;
  color: #333;
}

.video-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.video-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.video-info h4 {
  margin-bottom: 8px;
  color: #333;
}

.video-info p {
  margin-bottom: 5px;
  color: #666;
  font-size: 0.9rem;
}

.video-button {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  margin-top: 8px;
}

/* Mind Map */
.mindmap-container {
  text-align: center;
}

.mindmap-canvas {
  position: relative;
  height: 400px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.central-node {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 50%;
  font-weight: 600;
  min-width: 120px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.mindmap-branches {
  position: absolute;
  width: 100%;
  height: 100%;
}

.branch-node {
  position: absolute;
  background: white;
  padding: 10px 15px;
  border-radius: 20px;
  border: 2px solid #667eea;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.branch-0 { top: 20%; left: 20%; }
.branch-1 { top: 20%; right: 20%; }
.branch-2 { bottom: 20%; left: 20%; }
.branch-3 { bottom: 20%; right: 20%; }

.mindmap-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.mindmap-controls button {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

/* Flow Chart */
.flowchart-canvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: rgba(102, 126, 234, 0.02);
  border-radius: 12px;
  margin: 20px 0;
}

.flow-step {
  background: white;
  padding: 15px 25px;
  border-radius: 8px;
  border: 2px solid #667eea;
  font-weight: 500;
  text-align: center;
  min-width: 150px;
}

.flow-step.start,
.flow-step.end {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 25px;
}

.flow-step.decision {
  background: #ffd43b;
  color: #333;
  border-color: #ffd43b;
  transform: rotate(45deg);
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flow-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

.flow-branches {
  display: flex;
  gap: 50px;
  margin: 10px 0;
}

.flow-branch {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Quiz */
.quiz-container {
  max-width: 600px;
}

.quiz-question {
  background: rgba(102, 126, 234, 0.05);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.quiz-question h4 {
  margin-bottom: 15px;
  color: #333;
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quiz-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.quiz-option:hover {
  background: rgba(102, 126, 234, 0.05);
}

.quiz-submit {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 20px;
}

/* Calculator */
.calculator-tools {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.calc-tool {
  background: rgba(102, 126, 234, 0.05);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.calc-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.calc-inputs input {
  padding: 10px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 6px;
}

.calc-inputs button {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
}

/* Templates */
.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.template-item {
  background: rgba(102, 126, 234, 0.05);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.template-preview {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
  border-left: 4px solid #667eea;
}

/* Timeline */
.timeline {
  position: relative;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #667eea;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.timeline-marker {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.timeline-content {
  background: rgba(102, 126, 234, 0.05);
  padding: 15px;
  border-radius: 8px;
  flex: 1;
}

/* Game Elements */
.game-elements {
  margin-top: 20px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(102, 126, 234, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.achievements {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.achievement {
  background: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-tabs {
    flex-wrap: wrap;
  }
  
  .tab-button {
    flex: 1;
    min-width: 120px;
  }
  
  .viz-grid,
  .calculator-tools,
  .template-list {
    grid-template-columns: 1fr;
  }
  
  .flow-branches {
    flex-direction: column;
    gap: 20px;
  }
  
  .mindmap-canvas {
    height: 300px;
  }
  
  .branch-node {
    font-size: 0.8rem;
    padding: 8px 12px;
  }
}
