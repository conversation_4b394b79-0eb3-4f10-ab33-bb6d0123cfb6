# 🌳 Knowledge Tree Explorer

An interactive web application for exploring knowledge through gesture-based navigation, powered by OpenRouter API and DeepSeek R1.

## ✨ Features

### 🎯 Core Functionality
- **Automatic Knowledge Tree Generation**: Input any topic and get a comprehensive knowledge tree
- **Gesture-Based Navigation**: TikTok-style interface with intuitive gestures
- **Dynamic Article Generation**: On-demand content creation with customizable flags
- **Smart Storage**: Local storage for articles and navigation history

### 🤲 Gesture Controls
- **↓ Scroll Down**: Browse knowledge branches
- **↑ Scroll Up**: Generate article for selected branch
- **← Swipe Left**: Navigate back to previous level
- **V Gesture**: Text-to-speech for articles
- **S Gesture**: Save article locally
- **Tap**: Select branches and options

### 🏷️ Article Flags (Stackable)

#### 📝 Basic Content Flags
- `-a`: Standard comprehensive article
- `-t`: Table format with structured data
- `-ex`: Include 3 practical examples
- `-p`: Programming code demonstrations
- `-q`: Interactive quiz with 5 questions
- `-rap`: Exhaustive report format
- `-def`: Expert-level definitions and terminology

#### 🎓 Learning & Visualization
- `-path`: Creează parcursuri de învățare personalizate
- `-vis`: Generează infografice, diagrame și vizualizări interactive
- `-vid`: Sugerează videoclipuri relevante și creează script-uri video
- `-mind`: Prezintă informația ca mind map interactiv
- `-flow`: Creează diagrame de flux și procese

#### 🏭 Industry Specific
- `-case`: Studii de caz reale cu rezultate măsurabile
- `-scenario`: Scenarii practice și simulări
- `-lab`: Experimente și teste practice
- `-mentor`: Include sfaturi de la experți și mentori
- `-mistakes`: Analiza greșelilor comune și cum să le eviți

#### 🛠️ Interactive Tools
- `-calc`: Calculator și tool-uri interactive pentru calcule
- `-template`: Template-uri și checklist-uri acționabile
- `-workshop`: Format de workshop cu exerciții practice
- `-game`: Gamificare cu puncte, achievement-uri și competiții
- `-team`: Conținut optimizat pentru echipe și colaborare

#### 📤 Sharing & Presentation
- `-share`: Format pentru partajare ușoară cu colegii
- `-present`: Generează prezentări PowerPoint-style
- `-meeting`: Agenda și puncte de discuție pentru întâlniri
- `-offline`: Conținut disponibil offline

#### 📊 Analytics & Benchmarking
- `-kpi`: Include KPI-uri relevante și metrici de măsurat
- `-benchmark`: Comparații cu best practices din industrie
- `-timeline`: Planificare temporală și milestone-uri

#### 🌍 Localization
- `-ro`: Adaptat pentru piața și legislația românească
- `-eu`: Focalizat pe regulamentele și practicile UE
- `-local`: Include exemple și practici locale

#### ⚡ Advanced Features
- `-auto`: Automatizarea proceselor prezentate
- `-predict`: Predicții și tendințe bazate pe date
- `-optimize`: Sugestii de optimizare continuă

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- OpenRouter API key

### Installation

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd knowledge-tree-explorer
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your OpenRouter API key:
   ```env
   REACT_APP_OPENROUTER_API_KEY=your-api-key-here
   REACT_APP_SITE_URL=http://localhost:3000
   REACT_APP_SITE_NAME=Knowledge Tree Explorer
   ```

3. **Start development server**:
   ```bash
   npm start
   ```

4. **Open browser**: Navigate to `http://localhost:3000`

## 🔧 OpenRouter API Integration

The app uses OpenRouter's API with the DeepSeek R1 model for content generation:

```javascript
// Example API configuration
const client = new OpenRouterClient({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.REACT_APP_OPENROUTER_API_KEY,
  model: "deepseek/deepseek-r1-0528:free"
});
```

### API Features
- **Knowledge Tree Generation**: Creates structured learning paths
- **Dynamic Article Creation**: Generates content based on flags
- **Error Handling**: Graceful fallbacks for API failures
- **Rate Limiting**: Built-in request management

## 📱 Usage Guide

### 1. Topic Input
- Enter any topic (e.g., "Quantum Physics", "Machine Learning")
- Click "Explore Knowledge 🚀" or press Enter
- Wait for the knowledge tree to generate

### 2. Tree Navigation
- Browse branches by scrolling down
- Tap on a branch to select it
- Use gesture controls or buttons to generate articles

### 3. Article Customization
- Select multiple flags for enhanced content
- Flags are stackable (e.g., `-a -ex -q` for article + examples + quiz)
- Preview selected flags before generation

### 4. Gesture Navigation
- **Mobile**: Use touch gestures naturally
- **Desktop**: Use keyboard shortcuts (Arrow keys, V, S)
- **Debug Mode**: Press F1 to toggle gesture debugging

## 🎨 Architecture

### Component Structure
```
src/
├── components/
│   ├── TreeView.jsx          # Knowledge tree display
│   ├── ArticleView.jsx       # Article reader with TTS
│   ├── GestureHandler.jsx    # Gesture recognition
│   └── *.css                 # Component styles
├── services/
│   ├── openRouterService.js  # API integration
│   └── storageService.js     # Local storage management
└── App.jsx                   # Main application
```

### Key Technologies
- **React 18**: Modern React with hooks
- **OpenRouter API**: AI content generation
- **Web Speech API**: Text-to-speech functionality
- **LocalStorage**: Offline article storage
- **CSS3**: Modern styling with animations

## 🔧 Configuration

### Environment Variables
```env
# Required
REACT_APP_OPENROUTER_API_KEY=your-key

# Optional
REACT_APP_SITE_URL=your-site-url
REACT_APP_SITE_NAME=your-app-name
REACT_APP_DEBUG_MODE=false
```

### Customization Options
- **Theme**: Modify CSS variables for custom colors
- **Gestures**: Adjust sensitivity in GestureHandler
- **Storage**: Configure limits in storageService
- **API Model**: Change model in openRouterService

## 📊 Performance

### Optimization Features
- **Lazy Loading**: Components load on demand
- **Storage Limits**: Automatic cleanup of old data
- **Request Caching**: Reduces API calls
- **Responsive Design**: Mobile-first approach

### Storage Management
- **Articles**: Max 50 saved articles
- **History**: Max 20 navigation entries
- **Topics**: Max 10 recent topics
- **Auto-cleanup**: Removes oldest entries automatically

## 🧪 Testing

### Development Testing
```bash
# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

### Manual Testing
1. **Gesture Recognition**: Use F1 debug mode
2. **API Integration**: Test with various topics
3. **Storage**: Check browser DevTools > Application > LocalStorage
4. **Responsive**: Test on different screen sizes

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy Options
- **Netlify**: Drag and drop `build` folder
- **Vercel**: Connect GitHub repository
- **GitHub Pages**: Use `gh-pages` package
- **Custom Server**: Serve `build` folder

### Environment Setup
- Set production environment variables
- Configure CORS if using custom backend
- Enable HTTPS for production

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenRouter**: AI API platform
- **DeepSeek**: R1 language model
- **React Team**: Amazing framework
- **Community**: Open source contributors

## 📞 Support

- **Issues**: GitHub Issues tab
- **Documentation**: This README
- **Community**: Discussions tab

---

**Built with ❤️ for interactive learning and knowledge exploration**
