// LocalStorage implementation for saving articles and navigation history
export function saveArticle(article) {
  try {
    const savedArticles = getSavedArticles();
    savedArticles.push({
      ...article,
      savedAt: new Date().toISOString(),
    });
    
    localStorage.setItem('savedArticles', JSON.stringify(savedArticles));
    return true;
  } catch (error) {
    console.error('Error saving article:', error);
    return false;
  }
}

export function getSavedArticles() {
  try {
    const articles = localStorage.getItem('savedArticles');
    return articles ? JSON.parse(articles) : [];
  } catch (error) {
    console.error('Error retrieving saved articles:', error);
    return [];
  }
}

export function saveNavigationHistory(history) {
  try {
    localStorage.setItem('navigationHistory', JSON.stringify(history));
  } catch (error) {
    console.error('Error saving navigation history:', error);
  }
}

export function getNavigationHistory() {
  try {
    const history = localStorage.getItem('navigationHistory');
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('Error retrieving navigation history:', error);
    return [];
  }
}