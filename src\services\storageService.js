// LocalStorage service for Knowledge Tree App
const STORAGE_KEYS = {
  SAVED_ARTICLES: 'knowledgeTree_savedArticles',
  NAVIGATION_HISTORY: 'knowledgeTree_navigationHistory',
  USER_PREFERENCES: 'knowledgeTree_userPreferences',
  RECENT_TOPICS: 'knowledgeTree_recentTopics'
};

// Article Management
export function saveArticle(article) {
  try {
    if (!article || !article.titlu) {
      console.error('Invalid article data');
      return false;
    }

    const savedArticles = getSavedArticles();

    // Check if article already exists (avoid duplicates)
    const existingIndex = savedArticles.findIndex(
      saved => saved.titlu === article.titlu && saved.pozitie === article.pozitie
    );

    const articleToSave = {
      ...article,
      savedAt: new Date().toISOString(),
      id: generateArticleId(article)
    };

    if (existingIndex >= 0) {
      // Update existing article
      savedArticles[existingIndex] = articleToSave;
    } else {
      // Add new article
      savedArticles.push(articleToSave);
    }

    // Keep only the most recent 50 articles to prevent storage overflow
    const trimmedArticles = savedArticles
      .sort((a, b) => new Date(b.savedAt) - new Date(a.savedAt))
      .slice(0, 50);

    localStorage.setItem(STORAGE_KEYS.SAVED_ARTICLES, JSON.stringify(trimmedArticles));
    return true;
  } catch (error) {
    console.error('Error saving article:', error);
    return false;
  }
}

export function getSavedArticles() {
  try {
    const articles = localStorage.getItem(STORAGE_KEYS.SAVED_ARTICLES);
    return articles ? JSON.parse(articles) : [];
  } catch (error) {
    console.error('Error retrieving saved articles:', error);
    return [];
  }
}

export function deleteSavedArticle(articleId) {
  try {
    const savedArticles = getSavedArticles();
    const filteredArticles = savedArticles.filter(article => article.id !== articleId);
    localStorage.setItem(STORAGE_KEYS.SAVED_ARTICLES, JSON.stringify(filteredArticles));
    return true;
  } catch (error) {
    console.error('Error deleting saved article:', error);
    return false;
  }
}

// Navigation History Management
export function saveNavigationHistory(history) {
  try {
    // Keep only the most recent 20 navigation entries
    const trimmedHistory = history.slice(-20);
    localStorage.setItem(STORAGE_KEYS.NAVIGATION_HISTORY, JSON.stringify(trimmedHistory));
    return true;
  } catch (error) {
    console.error('Error saving navigation history:', error);
    return false;
  }
}

export function getNavigationHistory() {
  try {
    const history = localStorage.getItem(STORAGE_KEYS.NAVIGATION_HISTORY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('Error retrieving navigation history:', error);
    return [];
  }
}

// Recent Topics Management
export function saveRecentTopic(topic) {
  try {
    const recentTopics = getRecentTopics();

    // Remove topic if it already exists
    const filteredTopics = recentTopics.filter(t => t.topic !== topic);

    // Add to beginning of array
    const updatedTopics = [
      { topic, timestamp: new Date().toISOString() },
      ...filteredTopics
    ].slice(0, 10); // Keep only 10 most recent topics

    localStorage.setItem(STORAGE_KEYS.RECENT_TOPICS, JSON.stringify(updatedTopics));
    return true;
  } catch (error) {
    console.error('Error saving recent topic:', error);
    return false;
  }
}

export function getRecentTopics() {
  try {
    const topics = localStorage.getItem(STORAGE_KEYS.RECENT_TOPICS);
    return topics ? JSON.parse(topics) : [];
  } catch (error) {
    console.error('Error retrieving recent topics:', error);
    return [];
  }
}

// User Preferences Management
export function saveUserPreferences(preferences) {
  try {
    const currentPrefs = getUserPreferences();
    const updatedPrefs = { ...currentPrefs, ...preferences };
    localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(updatedPrefs));
    return true;
  } catch (error) {
    console.error('Error saving user preferences:', error);
    return false;
  }
}

export function getUserPreferences() {
  try {
    const preferences = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
    return preferences ? JSON.parse(preferences) : getDefaultPreferences();
  } catch (error) {
    console.error('Error retrieving user preferences:', error);
    return getDefaultPreferences();
  }
}

function getDefaultPreferences() {
  return {
    theme: 'light',
    textToSpeechEnabled: true,
    gestureDebugMode: false,
    defaultArticleFlags: ['-a'],
    autoSaveArticles: false,
    preferredLanguage: 'en'
  };
}

// Utility Functions
function generateArticleId(article) {
  // Generate a simple hash-based ID
  const str = `${article.titlu}_${article.pozitie}_${Date.now()}`;
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}