<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
    <meta name="theme-color" content="#667eea" />
    <meta name="description" content="Interactive Knowledge Tree Explorer with gesture-based navigation. Explore any topic through AI-generated knowledge trees and articles." />
    <meta name="keywords" content="knowledge tree, interactive learning, AI education, gesture navigation, OpenRouter, DeepSeek" />
    <meta name="author" content="Knowledge Tree Explorer Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="%PUBLIC_URL%" />
    <meta property="og:title" content="Knowledge Tree Explorer - Interactive Learning" />
    <meta property="og:description" content="Explore knowledge through interactive trees with gesture-based navigation. AI-powered content generation for any topic." />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="%PUBLIC_URL%" />
    <meta property="twitter:title" content="Knowledge Tree Explorer - Interactive Learning" />
    <meta property="twitter:description" content="Explore knowledge through interactive trees with gesture-based navigation. AI-powered content generation for any topic." />
    <meta property="twitter:image" content="%PUBLIC_URL%/og-image.png" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://openrouter.ai" />
    <link rel="dns-prefetch" href="https://openrouter.ai" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <title>Knowledge Tree Explorer - Interactive Learning</title>
    
    <!-- Inline critical CSS for faster loading -->
    <style>
      /* Critical CSS for initial load */
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }
      
      /* Loading spinner */
      .initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
      }
      
      .loader-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.2);
        border-left: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loader-text {
        font-size: 1.2rem;
        font-weight: 600;
        text-align: center;
        opacity: 0.9;
      }
      
      /* Hide loader when React loads */
      .loaded .initial-loader {
        display: none;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="padding: 20px; text-align: center; background: #ff4757; color: white;">
        <h2>JavaScript Required</h2>
        <p>You need to enable JavaScript to run this app. Knowledge Tree Explorer requires JavaScript for interactive features and gesture recognition.</p>
      </div>
    </noscript>
    
    <!-- Initial loading screen -->
    <div class="initial-loader" id="initial-loader">
      <div class="loader-spinner"></div>
      <div class="loader-text">
        🌳 Loading Knowledge Tree Explorer...
      </div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Service Worker Registration -->
    <script>
      // Hide loader when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('loaded');
        }, 500);
      });
      
      // Register service worker for offline functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('%PUBLIC_URL%/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
      
      // Gesture detection setup
      if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
        document.body.classList.add('touch-device');
      }
      
      // Prevent zoom on double tap for better gesture recognition
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // Prevent context menu on long press
      document.addEventListener('contextmenu', function(e) {
        if (e.target.closest('.gesture-container')) {
          e.preventDefault();
        }
      });
      
      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', function() {
          setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
              console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }
          }, 0);
        });
      }
    </script>
  </body>
</html>
