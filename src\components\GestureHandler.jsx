import { useEffect, useRef, useState } from 'react';
import './GestureHandler.css';

export default function GestureHandler({
  onVerticalScroll,
  onReverseScroll,
  onLateralScroll,
  onVGesture,
  onSGesture,
  onTap,
  children
}) {
  const containerRef = useRef(null);
  const [gestureDebug, setGestureDebug] = useState('');
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    const element = containerRef.current;
    if (!element) return;

    let startX, startY, endX, endY;
    let startTime, endTime;
    let gestureTimeout;
    let touchCount = 0;
    let gesturePoints = [];

    // Gesture detection parameters
    const SWIPE_THRESHOLD = 50;
    const SWIPE_VELOCITY_THRESHOLD = 0.3;
    const TAP_THRESHOLD = 10;
    const TAP_TIME_THRESHOLD = 300;
    const GESTURE_RECOGNITION_TIME = 1000;

    const handleTouchStart = (e) => {
      touchCount = e.touches.length;
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      startTime = Date.now();
      gesturePoints = [{ x: startX, y: startY, time: startTime }];

      clearTimeout(gestureTimeout);
      setGestureDebug(`Touch start: ${touchCount} finger(s)`);
    };

    const handleTouchMove = (e) => {
      e.preventDefault(); // Prevent scrolling
      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const currentTime = Date.now();

      gesturePoints.push({ x: currentX, y: currentY, time: currentTime });

      // Keep only recent points for gesture recognition
      gesturePoints = gesturePoints.filter(point =>
        currentTime - point.time < GESTURE_RECOGNITION_TIME
      );
    };

    const handleTouchEnd = (e) => {
      endX = e.changedTouches[0].clientX;
      endY = e.changedTouches[0].clientY;
      endTime = Date.now();

      const diffX = endX - startX;
      const diffY = endY - startY;
      const distance = Math.sqrt(diffX * diffX + diffY * diffY);
      const duration = endTime - startTime;
      const velocity = distance / duration;

      setGestureDebug(`End: dx=${diffX.toFixed(0)}, dy=${diffY.toFixed(0)}, v=${velocity.toFixed(2)}`);

      // Detect gesture type
      if (distance < TAP_THRESHOLD && duration < TAP_TIME_THRESHOLD) {
        // Tap gesture
        handleTapGesture(e.changedTouches[0]);
      } else if (distance > SWIPE_THRESHOLD && velocity > SWIPE_VELOCITY_THRESHOLD) {
        // Swipe gesture
        handleSwipeGesture(diffX, diffY, velocity);
      } else if (gesturePoints.length > 5) {
        // Complex gesture (V or S)
        handleComplexGesture(gesturePoints);
      }

      // Reset
      gesturePoints = [];
      touchCount = 0;
    };

    const handleTapGesture = (touch) => {
      const target = document.elementFromPoint(touch.clientX, touch.clientY);
      setGestureDebug('Tap detected');
      onTap && onTap(target);
    };

    const handleSwipeGesture = (diffX, diffY, velocity) => {
      const absX = Math.abs(diffX);
      const absY = Math.abs(diffY);

      if (absX > absY) {
        // Horizontal swipe
        if (diffX > 0) {
          setGestureDebug('Swipe right');
          // Right swipe - could be used for forward navigation
        } else {
          setGestureDebug('Swipe left - Go back');
          onLateralScroll && onLateralScroll();
        }
      } else {
        // Vertical swipe
        if (diffY > 0) {
          setGestureDebug('Swipe down - Browse branches');
          onVerticalScroll && onVerticalScroll('down');
        } else {
          setGestureDebug('Swipe up - Generate article');
          onReverseScroll && onReverseScroll();
        }
      }
    };

    const handleComplexGesture = (points) => {
      // Analyze gesture pattern for V or S shapes
      const gesture = recognizeGesturePattern(points);

      if (gesture === 'V') {
        setGestureDebug('V gesture - Text-to-speech');
        onVGesture && onVGesture();
      } else if (gesture === 'S') {
        setGestureDebug('S gesture - Save article');
        onSGesture && onSGesture();
      }
    };

    const recognizeGesturePattern = (points) => {
      if (points.length < 5) return null;

      // Simplified pattern recognition
      const firstHalf = points.slice(0, Math.floor(points.length / 2));
      const secondHalf = points.slice(Math.floor(points.length / 2));

      // Check for V pattern (down then up)
      const firstHalfTrend = getTrend(firstHalf);
      const secondHalfTrend = getTrend(secondHalf);

      if (firstHalfTrend === 'down' && secondHalfTrend === 'up') {
        return 'V';
      }

      // Check for S pattern (alternating directions)
      const changes = getDirectionChanges(points);
      if (changes >= 2) {
        return 'S';
      }

      return null;
    };

    const getTrend = (points) => {
      if (points.length < 2) return null;
      const start = points[0];
      const end = points[points.length - 1];
      return end.y > start.y ? 'down' : 'up';
    };

    const getDirectionChanges = (points) => {
      let changes = 0;
      let lastDirection = null;

      for (let i = 1; i < points.length; i++) {
        const currentDirection = points[i].y > points[i-1].y ? 'down' : 'up';
        if (lastDirection && currentDirection !== lastDirection) {
          changes++;
        }
        lastDirection = currentDirection;
      }

      return changes;
    };

    // Keyboard shortcuts for desktop testing
    const handleKeyDown = (e) => {
      switch(e.key) {
        case 'ArrowUp':
          e.preventDefault();
          onReverseScroll && onReverseScroll();
          setGestureDebug('Keyboard: Arrow Up - Generate article');
          break;
        case 'ArrowDown':
          e.preventDefault();
          onVerticalScroll && onVerticalScroll('down');
          setGestureDebug('Keyboard: Arrow Down - Browse branches');
          break;
        case 'ArrowLeft':
          e.preventDefault();
          onLateralScroll && onLateralScroll();
          setGestureDebug('Keyboard: Arrow Left - Go back');
          break;
        case 'v':
        case 'V':
          onVGesture && onVGesture();
          setGestureDebug('Keyboard: V - Text-to-speech');
          break;
        case 's':
        case 'S':
          if (!e.ctrlKey) { // Avoid conflict with Ctrl+S
            onSGesture && onSGesture();
            setGestureDebug('Keyboard: S - Save article');
          }
          break;
        case 'F1':
          e.preventDefault();
          setShowDebug(!showDebug);
          break;
      }
    };

    // Add event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('keydown', handleKeyDown);
      clearTimeout(gestureTimeout);
    };
  }, [onVerticalScroll, onReverseScroll, onLateralScroll, onVGesture, onSGesture, onTap]);

  return (
    <div ref={containerRef} className="gesture-container">
      {children}

      {/* Debug overlay */}
      {showDebug && (
        <div className="gesture-debug">
          <div className="debug-header">
            <span>Gesture Debug</span>
            <button onClick={() => setShowDebug(false)}>×</button>
          </div>
          <div className="debug-content">
            <p>{gestureDebug || 'No gesture detected'}</p>
            <div className="debug-help">
              <h4>Controls:</h4>
              <ul>
                <li>↑/Swipe up: Generate article</li>
                <li>↓/Swipe down: Browse branches</li>
                <li>←/Swipe left: Go back</li>
                <li>V key/V gesture: Text-to-speech</li>
                <li>S key/S gesture: Save article</li>
                <li>F1: Toggle debug</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}