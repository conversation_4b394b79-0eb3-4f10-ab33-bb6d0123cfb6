# OpenRouter API Configuration
REACT_APP_OPENROUTER_API_KEY=your-openrouter-api-key-here

# Site Configuration for OpenRouter Rankings
REACT_APP_SITE_URL=http://localhost:3000
REACT_APP_SITE_NAME=Knowledge Tree Explorer

# Optional: Custom API Model (default: deepseek/deepseek-r1-0528:free)
REACT_APP_OPENROUTER_MODEL=deepseek/deepseek-r1-0528:free

# Development Settings
REACT_APP_DEBUG_MODE=false
REACT_APP_ENABLE_GESTURE_DEBUG=false

# Performance Settings
REACT_APP_MAX_SAVED_ARTICLES=50
REACT_APP_MAX_NAVIGATION_HISTORY=20
REACT_APP_MAX_RECENT_TOPICS=10

# Feature Flags
REACT_APP_ENABLE_TEXT_TO_SPEECH=true
REACT_APP_ENABLE_OFFLINE_MODE=true
REACT_APP_ENABLE_ANALYTICS=false
