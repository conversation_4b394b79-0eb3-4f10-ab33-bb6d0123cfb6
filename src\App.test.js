import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import App from './App';

// Mock the OpenRouter service
jest.mock('./services/openRouterService', () => ({
  generateKnowledgeTree: jest.fn(),
  generateArticle: jest.fn(),
  testConnection: jest.fn()
}));

// Mock the storage service
jest.mock('./services/storageService', () => ({
  saveArticle: jest.fn(() => true),
  getSavedArticles: jest.fn(() => []),
  saveNavigationHistory: jest.fn(() => true),
  getNavigationHistory: jest.fn(() => []),
  saveRecentTopic: jest.fn(() => true),
  getRecentTopics: jest.fn(() => [])
}));

describe('Knowledge Tree Explorer App', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders topic input screen initially', () => {
    render(<App />);
    
    // Check if the main heading is present
    expect(screen.getByText('🌳 Knowledge Tree Explorer')).toBeInTheDocument();
    
    // Check if the input field is present
    expect(screen.getByPlaceholderText(/e.g., Quantum Physics/i)).toBeInTheDocument();
    
    // Check if the explore button is present
    expect(screen.getByText(/Explore Knowledge/i)).toBeInTheDocument();
  });

  test('shows gesture hints on initial screen', () => {
    render(<App />);
    
    // Check if gesture controls section is present
    expect(screen.getByText('📱 Gesture Controls:')).toBeInTheDocument();
    
    // Check for specific gesture instructions
    expect(screen.getByText(/↓ Scroll down: Browse branches/i)).toBeInTheDocument();
    expect(screen.getByText(/↑ Scroll up: Generate article/i)).toBeInTheDocument();
    expect(screen.getByText(/← Swipe left: Go back/i)).toBeInTheDocument();
  });

  test('input field accepts text input', () => {
    render(<App />);
    
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    
    fireEvent.change(input, { target: { value: 'Machine Learning' } });
    
    expect(input.value).toBe('Machine Learning');
  });

  test('explore button is disabled when input is empty', () => {
    render(<App />);
    
    const button = screen.getByText(/Explore Knowledge/i);
    
    expect(button).toBeDisabled();
  });

  test('explore button is enabled when input has text', () => {
    render(<App />);
    
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    const button = screen.getByText(/Explore Knowledge/i);
    
    fireEvent.change(input, { target: { value: 'Test Topic' } });
    
    expect(button).not.toBeDisabled();
  });

  test('form submission triggers topic generation', async () => {
    const { generateKnowledgeTree } = require('./services/openRouterService');
    
    // Mock successful API response
    generateKnowledgeTree.mockResolvedValue({
      tema: 'Test Topic',
      ramuri: [
        {
          nume: 'Test Branch',
          descriere: 'Test description',
          emoji: '🧪',
          subcategorii: ['Sub1', 'Sub2']
        }
      ]
    });

    render(<App />);
    
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    const button = screen.getByText(/Explore Knowledge/i);
    
    fireEvent.change(input, { target: { value: 'Test Topic' } });
    fireEvent.click(button);
    
    // Check if loading state is shown
    expect(screen.getByText(/Generating.../i)).toBeInTheDocument();
    
    // Wait for API call to complete
    await waitFor(() => {
      expect(generateKnowledgeTree).toHaveBeenCalledWith('Test Topic');
    });
  });

  test('error handling displays error message', async () => {
    const { generateKnowledgeTree } = require('./services/openRouterService');
    
    // Mock API error
    generateKnowledgeTree.mockRejectedValue(new Error('API Error'));

    render(<App />);
    
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    const button = screen.getByText(/Explore Knowledge/i);
    
    fireEvent.change(input, { target: { value: 'Test Topic' } });
    fireEvent.click(button);
    
    // Wait for error to be displayed
    await waitFor(() => {
      expect(screen.getByText(/Failed to generate knowledge tree/i)).toBeInTheDocument();
    });
  });

  test('keyboard shortcuts work for form submission', () => {
    render(<App />);
    
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    
    fireEvent.change(input, { target: { value: 'Test Topic' } });
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
    
    // Should trigger form submission (loading state)
    expect(screen.getByText(/Generating.../i)).toBeInTheDocument();
  });

  test('gesture handler is rendered', () => {
    render(<App />);
    
    // Check if gesture container is present
    const gestureContainer = document.querySelector('.gesture-container');
    expect(gestureContainer).toBeInTheDocument();
  });

  test('app handles touch device detection', () => {
    // Mock touch device
    Object.defineProperty(window, 'ontouchstart', {
      value: true,
      writable: true
    });

    render(<App />);
    
    // App should render normally on touch devices
    expect(screen.getByText('🌳 Knowledge Tree Explorer')).toBeInTheDocument();
  });
});

// Integration tests
describe('Knowledge Tree Explorer Integration', () => {
  test('complete flow from topic input to tree view', async () => {
    const { generateKnowledgeTree } = require('./services/openRouterService');

    const mockTree = {
      tema: 'Artificial Intelligence',
      ramuri: [
        {
          nume: 'Machine Learning',
          descriere: 'Algorithms that learn from data',
          emoji: '🤖',
          subcategorii: ['Supervised Learning', 'Unsupervised Learning', 'Deep Learning']
        },
        {
          nume: 'Natural Language Processing',
          descriere: 'Understanding and processing human language',
          emoji: '💬',
          subcategorii: ['Text Analysis', 'Speech Recognition', 'Language Generation']
        }
      ]
    };

    generateKnowledgeTree.mockResolvedValue(mockTree);

    render(<App />);

    // Enter topic
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    fireEvent.change(input, { target: { value: 'Artificial Intelligence' } });

    // Submit form
    const button = screen.getByText(/Explore Knowledge/i);
    fireEvent.click(button);

    // Wait for tree to load
    await waitFor(() => {
      expect(screen.getByText('Artificial Intelligence')).toBeInTheDocument();
      expect(screen.getByText('Machine Learning')).toBeInTheDocument();
      expect(screen.getByText('Natural Language Processing')).toBeInTheDocument();
    });

    // Check if branches are clickable
    const branchItem = screen.getByText('Machine Learning');
    expect(branchItem).toBeInTheDocument();
  });

  test('enhanced flags are available in tree view', async () => {
    const { generateKnowledgeTree } = require('./services/openRouterService');

    const mockTree = {
      tema: 'Test Topic',
      ramuri: [
        {
          nume: 'Test Branch',
          descriere: 'Test description',
          emoji: '🧪',
          subcategorii: ['Sub1', 'Sub2']
        }
      ]
    };

    generateKnowledgeTree.mockResolvedValue(mockTree);

    render(<App />);

    // Navigate to tree view
    const input = screen.getByPlaceholderText(/e.g., Quantum Physics/i);
    fireEvent.change(input, { target: { value: 'Test Topic' } });
    fireEvent.click(screen.getByText(/Explore Knowledge/i));

    await waitFor(() => {
      expect(screen.getByText('Test Topic')).toBeInTheDocument();
    });

    // Click on branch to select it
    const branchItem = screen.getByText('Test Branch');
    fireEvent.click(branchItem);

    // Click customize flags button
    const flagsButton = screen.getByText(/Customize Flags/i);
    fireEvent.click(flagsButton);

    // Check if enhanced flags are available
    await waitFor(() => {
      expect(screen.getByText('Learning Path')).toBeInTheDocument();
      expect(screen.getByText('Visualizations')).toBeInTheDocument();
      expect(screen.getByText('Romanian Market')).toBeInTheDocument();
      expect(screen.getByText('Automation')).toBeInTheDocument();
    });
  });
});
