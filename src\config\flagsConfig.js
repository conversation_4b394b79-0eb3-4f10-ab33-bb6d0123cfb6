// Comprehensive flags configuration for Knowledge Tree Explorer
export const FLAG_CATEGORIES = {
  basic: {
    name: 'Basic Content',
    icon: '📝',
    description: 'Standard content generation options',
    color: '#667eea'
  },
  learning: {
    name: 'Learning & Visualization',
    icon: '🎓',
    description: 'Educational tools and visual content',
    color: '#51cf66'
  },
  industry: {
    name: 'Industry Specific',
    icon: '🏭',
    description: 'Professional and industry-focused content',
    color: '#ff6b6b'
  },
  tools: {
    name: 'Interactive Tools',
    icon: '🛠️',
    description: 'Interactive and collaborative features',
    color: '#ffd43b'
  },
  sharing: {
    name: 'Sharing & Presentation',
    icon: '📤',
    description: 'Content optimized for sharing and presenting',
    color: '#764ba2'
  },
  analytics: {
    name: 'Analytics & Benchmarking',
    icon: '📊',
    description: 'Data-driven insights and performance metrics',
    color: '#20c997'
  },
  localization: {
    name: 'Localization',
    icon: '🌍',
    description: 'Region and language-specific adaptations',
    color: '#fd7e14'
  },
  advanced: {
    name: 'Advanced Features',
    icon: '⚡',
    description: 'Cutting-edge and automation features',
    color: '#e83e8c'
  }
};

export const FLAGS_CONFIG = [
  // Basic Content Flags
  {
    flag: '-a',
    label: 'Standard Article',
    description: 'Comprehensive overview with structured content',
    category: 'basic',
    complexity: 'low',
    estimatedTime: '5-10 min',
    popularityScore: 10
  },
  {
    flag: '-t',
    label: 'Table Format',
    description: 'Structured data tables and comparisons',
    category: 'basic',
    complexity: 'low',
    estimatedTime: '3-7 min',
    popularityScore: 7
  },
  {
    flag: '-ex',
    label: '3 Examples',
    description: 'Practical examples with detailed explanations',
    category: 'basic',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 9
  },
  {
    flag: '-p',
    label: 'Code Demo',
    description: 'Programming examples and technical demonstrations',
    category: 'basic',
    complexity: 'high',
    estimatedTime: '10-20 min',
    popularityScore: 6
  },
  {
    flag: '-q',
    label: 'Quiz Mode',
    description: '5-question interactive quiz',
    category: 'basic',
    complexity: 'medium',
    estimatedTime: '5-10 min',
    popularityScore: 8
  },
  {
    flag: '-rap',
    label: 'Full Report',
    description: 'Exhaustive coverage with comprehensive analysis',
    category: 'basic',
    complexity: 'high',
    estimatedTime: '15-30 min',
    popularityScore: 5
  },
  {
    flag: '-def',
    label: 'Expert Definitions',
    description: 'Technical terminology and expert-level concepts',
    category: 'basic',
    complexity: 'high',
    estimatedTime: '7-12 min',
    popularityScore: 6
  },

  // Learning & Visualization
  {
    flag: '-path',
    label: 'Learning Path',
    description: 'Personalized progression with milestones',
    category: 'learning',
    complexity: 'medium',
    estimatedTime: '10-20 min',
    popularityScore: 8
  },
  {
    flag: '-vis',
    label: 'Visualizations',
    description: 'Infographics, diagrams, and interactive visualizations',
    category: 'learning',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 9
  },
  {
    flag: '-vid',
    label: 'Video Scripts',
    description: 'Video suggestions and detailed scripts',
    category: 'learning',
    complexity: 'medium',
    estimatedTime: '12-25 min',
    popularityScore: 7
  },
  {
    flag: '-mind',
    label: 'Mind Map',
    description: 'Interactive mind mapping with connected concepts',
    category: 'learning',
    complexity: 'medium',
    estimatedTime: '6-12 min',
    popularityScore: 8
  },
  {
    flag: '-flow',
    label: 'Flow Charts',
    description: 'Process diagrams and decision flowcharts',
    category: 'learning',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 7
  },

  // Industry Specific
  {
    flag: '-case',
    label: 'Case Studies',
    description: 'Real case studies with measurable results',
    category: 'industry',
    complexity: 'high',
    estimatedTime: '15-25 min',
    popularityScore: 9
  },
  {
    flag: '-scenario',
    label: 'Scenarios',
    description: 'Practical scenarios and business simulations',
    category: 'industry',
    complexity: 'medium',
    estimatedTime: '10-18 min',
    popularityScore: 7
  },
  {
    flag: '-lab',
    label: 'Lab Experiments',
    description: 'Hands-on experiments and practical testing',
    category: 'industry',
    complexity: 'high',
    estimatedTime: '20-40 min',
    popularityScore: 6
  },
  {
    flag: '-mentor',
    label: 'Expert Advice',
    description: 'Professional mentorship and industry insights',
    category: 'industry',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 8
  },
  {
    flag: '-mistakes',
    label: 'Common Mistakes',
    description: 'Error analysis and prevention strategies',
    category: 'industry',
    complexity: 'medium',
    estimatedTime: '6-12 min',
    popularityScore: 8
  },

  // Interactive Tools
  {
    flag: '-calc',
    label: 'Calculators',
    description: 'Interactive calculation tools and formulas',
    category: 'tools',
    complexity: 'high',
    estimatedTime: '10-20 min',
    popularityScore: 7
  },
  {
    flag: '-template',
    label: 'Templates',
    description: 'Actionable templates and checklists',
    category: 'tools',
    complexity: 'low',
    estimatedTime: '5-10 min',
    popularityScore: 9
  },
  {
    flag: '-workshop',
    label: 'Workshop Format',
    description: 'Group exercises and collaborative activities',
    category: 'tools',
    complexity: 'medium',
    estimatedTime: '15-30 min',
    popularityScore: 6
  },
  {
    flag: '-game',
    label: 'Gamification',
    description: 'Points, achievements, and competitive elements',
    category: 'tools',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 7
  },
  {
    flag: '-team',
    label: 'Team Content',
    description: 'Collaboration-focused content and team exercises',
    category: 'tools',
    complexity: 'medium',
    estimatedTime: '10-20 min',
    popularityScore: 6
  },

  // Sharing & Presentation
  {
    flag: '-share',
    label: 'Easy Sharing',
    description: 'Colleague-friendly format with summary points',
    category: 'sharing',
    complexity: 'low',
    estimatedTime: '5-8 min',
    popularityScore: 8
  },
  {
    flag: '-present',
    label: 'Presentation',
    description: 'PowerPoint-style slides with talking points',
    category: 'sharing',
    complexity: 'medium',
    estimatedTime: '10-15 min',
    popularityScore: 7
  },
  {
    flag: '-meeting',
    label: 'Meeting Agenda',
    description: 'Discussion points with time allocations',
    category: 'sharing',
    complexity: 'low',
    estimatedTime: '5-10 min',
    popularityScore: 6
  },
  {
    flag: '-offline',
    label: 'Offline Ready',
    description: 'Downloadable resources for offline access',
    category: 'sharing',
    complexity: 'low',
    estimatedTime: '3-6 min',
    popularityScore: 5
  },

  // Analytics & Benchmarking
  {
    flag: '-kpi',
    label: 'KPIs & Metrics',
    description: 'Key performance indicators and tracking methods',
    category: 'analytics',
    complexity: 'high',
    estimatedTime: '12-20 min',
    popularityScore: 7
  },
  {
    flag: '-benchmark',
    label: 'Benchmarking',
    description: 'Industry best practices and competitive analysis',
    category: 'analytics',
    complexity: 'high',
    estimatedTime: '15-25 min',
    popularityScore: 6
  },
  {
    flag: '-timeline',
    label: 'Timeline Planning',
    description: 'Milestones and realistic project deadlines',
    category: 'analytics',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 7
  },

  // Localization
  {
    flag: '-ro',
    label: 'Romanian Market',
    description: 'Local legislation, examples, and market specifics',
    category: 'localization',
    complexity: 'medium',
    estimatedTime: '10-18 min',
    popularityScore: 4
  },
  {
    flag: '-eu',
    label: 'EU Focused',
    description: 'European regulations and compliance guidelines',
    category: 'localization',
    complexity: 'high',
    estimatedTime: '12-20 min',
    popularityScore: 5
  },
  {
    flag: '-local',
    label: 'Local Practices',
    description: 'Regional examples and area-specific considerations',
    category: 'localization',
    complexity: 'medium',
    estimatedTime: '8-15 min',
    popularityScore: 5
  },

  // Advanced Features
  {
    flag: '-auto',
    label: 'Automation',
    description: 'Process automation tools and implementation',
    category: 'advanced',
    complexity: 'high',
    estimatedTime: '15-30 min',
    popularityScore: 6
  },
  {
    flag: '-predict',
    label: 'Predictions',
    description: 'Data-based trends and future forecasting',
    category: 'advanced',
    complexity: 'high',
    estimatedTime: '12-25 min',
    popularityScore: 5
  },
  {
    flag: '-optimize',
    label: 'Optimization',
    description: 'Continuous improvement and performance enhancement',
    category: 'advanced',
    complexity: 'high',
    estimatedTime: '10-20 min',
    popularityScore: 6
  }
];

// Predefined flag combinations
export const POPULAR_COMBINATIONS = {
  beginner: {
    name: 'Beginner Friendly',
    flags: ['-a', '-ex', '-vis'],
    description: 'Perfect for newcomers to the topic'
  },
  professional: {
    name: 'Professional',
    flags: ['-a', '-case', '-kpi', '-benchmark'],
    description: 'Business-focused with real-world applications'
  },
  comprehensive: {
    name: 'Comprehensive',
    flags: ['-rap', '-vis', '-case', '-template'],
    description: 'Complete coverage with practical tools'
  },
  interactive: {
    name: 'Interactive Learning',
    flags: ['-vis', '-mind', '-q', '-game'],
    description: 'Engaging and interactive content'
  },
  romanian: {
    name: 'Romanian Market',
    flags: ['-a', '-ro', '-case', '-template'],
    description: 'Adapted for Romanian business environment'
  },
  technical: {
    name: 'Technical Deep Dive',
    flags: ['-def', '-p', '-lab', '-calc'],
    description: 'Technical implementation with code examples'
  }
};

// Utility functions
export const getFlagsByCategory = (category) => {
  return FLAGS_CONFIG.filter(flag => flag.category === category);
};

export const getPopularFlags = (limit = 10) => {
  return FLAGS_CONFIG
    .sort((a, b) => b.popularityScore - a.popularityScore)
    .slice(0, limit);
};

export const getFlagConfig = (flagName) => {
  return FLAGS_CONFIG.find(flag => flag.flag === flagName);
};

export const estimateTotalTime = (selectedFlags) => {
  const totalMinutes = selectedFlags.reduce((total, flagName) => {
    const config = getFlagConfig(flagName);
    if (config && config.estimatedTime) {
      const timeRange = config.estimatedTime.match(/(\d+)-(\d+)/);
      if (timeRange) {
        const avgTime = (parseInt(timeRange[1]) + parseInt(timeRange[2])) / 2;
        return total + avgTime;
      }
    }
    return total + 5; // Default 5 minutes if no time specified
  }, 0);

  return `${Math.round(totalMinutes)} minutes`;
};

export const getComplexityLevel = (selectedFlags) => {
  const complexities = selectedFlags.map(flagName => {
    const config = getFlagConfig(flagName);
    return config?.complexity || 'low';
  });

  if (complexities.includes('high')) return 'Advanced';
  if (complexities.includes('medium')) return 'Intermediate';
  return 'Beginner';
};
