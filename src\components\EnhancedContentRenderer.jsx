import { useState, useEffect } from 'react';
import './EnhancedContentRenderer.css';

export default function EnhancedContentRenderer({ article, flags }) {
  const [activeTab, setActiveTab] = useState('content');
  const [quizAnswers, setQuizAnswers] = useState({});
  const [calculatorValues, setCalculatorValues] = useState({});

  // Check which enhanced features are available
  const hasVisualization = flags.includes('-vis');
  const hasVideo = flags.includes('-vid');
  const hasMindMap = flags.includes('-mind');
  const hasFlowChart = flags.includes('-flow');
  const hasQuiz = flags.includes('-q');
  const hasCalculator = flags.includes('-calc');
  const hasTemplate = flags.includes('-template');
  const hasWorkshop = flags.includes('-workshop');
  const hasGameification = flags.includes('-game');
  const hasTimeline = flags.includes('-timeline');

  const tabs = [
    { id: 'content', label: 'Article', icon: '📄' },
    ...(hasVisualization ? [{ id: 'visualizations', label: 'Visualizations', icon: '📊' }] : []),
    ...(hasVideo ? [{ id: 'videos', label: 'Videos', icon: '🎥' }] : []),
    ...(hasMindMap ? [{ id: 'mindmap', label: 'Mind Map', icon: '🧠' }] : []),
    ...(hasFlowChart ? [{ id: 'flowchart', label: 'Flow Chart', icon: '🔄' }] : []),
    ...(hasQuiz ? [{ id: 'quiz', label: 'Quiz', icon: '❓' }] : []),
    ...(hasCalculator ? [{ id: 'calculator', label: 'Calculator', icon: '🧮' }] : []),
    ...(hasTemplate ? [{ id: 'templates', label: 'Templates', icon: '📋' }] : []),
    ...(hasWorkshop ? [{ id: 'workshop', label: 'Workshop', icon: '🛠️' }] : []),
    ...(hasTimeline ? [{ id: 'timeline', label: 'Timeline', icon: '📅' }] : [])
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'content':
        return renderArticleContent();
      case 'visualizations':
        return renderVisualizations();
      case 'videos':
        return renderVideoSuggestions();
      case 'mindmap':
        return renderMindMap();
      case 'flowchart':
        return renderFlowChart();
      case 'quiz':
        return renderQuiz();
      case 'calculator':
        return renderCalculator();
      case 'templates':
        return renderTemplates();
      case 'workshop':
        return renderWorkshop();
      case 'timeline':
        return renderTimeline();
      default:
        return renderArticleContent();
    }
  };

  const renderArticleContent = () => {
    return (
      <div className="article-content-enhanced">
        {formatArticleContent(article.continut)}
        {hasGameification && renderGameElements()}
      </div>
    );
  };

  const renderVisualizations = () => {
    return (
      <div className="visualizations-container">
        <h3>📊 Interactive Visualizations</h3>
        <div className="viz-grid">
          <div className="viz-item">
            <h4>Concept Diagram</h4>
            <div className="viz-placeholder">
              <p>🎯 Interactive diagram showing key concepts and relationships</p>
              <button className="viz-button">Generate Diagram</button>
            </div>
          </div>
          <div className="viz-item">
            <h4>Process Infographic</h4>
            <div className="viz-placeholder">
              <p>📈 Step-by-step visual process breakdown</p>
              <button className="viz-button">Create Infographic</button>
            </div>
          </div>
          <div className="viz-item">
            <h4>Data Visualization</h4>
            <div className="viz-placeholder">
              <p>📊 Charts and graphs for better understanding</p>
              <button className="viz-button">Show Charts</button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderVideoSuggestions = () => {
    return (
      <div className="video-suggestions">
        <h3>🎥 Video Resources & Scripts</h3>
        <div className="video-list">
          <div className="video-item">
            <div className="video-thumbnail">📹</div>
            <div className="video-info">
              <h4>Introduction to {article.titlu}</h4>
              <p>Duration: 5-7 minutes</p>
              <p>Script: Overview of key concepts with visual examples</p>
              <button className="video-button">View Script</button>
            </div>
          </div>
          <div className="video-item">
            <div className="video-thumbnail">🎬</div>
            <div className="video-info">
              <h4>Practical Implementation</h4>
              <p>Duration: 10-15 minutes</p>
              <p>Script: Step-by-step implementation guide</p>
              <button className="video-button">View Script</button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderMindMap = () => {
    return (
      <div className="mindmap-container">
        <h3>🧠 Interactive Mind Map</h3>
        <div className="mindmap-canvas">
          <div className="mindmap-center">
            <div className="central-node">{article.titlu}</div>
          </div>
          <div className="mindmap-branches">
            {article.subcategorii?.map((sub, index) => (
              <div key={index} className={`branch-node branch-${index % 4}`}>
                {sub}
              </div>
            ))}
          </div>
        </div>
        <div className="mindmap-controls">
          <button>Expand All</button>
          <button>Focus Mode</button>
          <button>Export</button>
        </div>
      </div>
    );
  };

  const renderFlowChart = () => {
    return (
      <div className="flowchart-container">
        <h3>🔄 Process Flow Chart</h3>
        <div className="flowchart-canvas">
          <div className="flow-step start">Start</div>
          <div className="flow-arrow">↓</div>
          <div className="flow-step process">Analyze Requirements</div>
          <div className="flow-arrow">↓</div>
          <div className="flow-step decision">Decision Point</div>
          <div className="flow-branches">
            <div className="flow-branch">
              <div className="flow-arrow">→</div>
              <div className="flow-step process">Option A</div>
            </div>
            <div className="flow-branch">
              <div className="flow-arrow">→</div>
              <div className="flow-step process">Option B</div>
            </div>
          </div>
          <div className="flow-step end">End Result</div>
        </div>
      </div>
    );
  };

  const renderQuiz = () => {
    const questions = [
      {
        question: `What is the main concept of ${article.titlu}?`,
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correct: 0
      },
      {
        question: 'Which approach is most effective?',
        options: ['Approach 1', 'Approach 2', 'Approach 3', 'Approach 4'],
        correct: 1
      }
    ];

    return (
      <div className="quiz-container">
        <h3>❓ Knowledge Quiz</h3>
        {questions.map((q, index) => (
          <div key={index} className="quiz-question">
            <h4>Question {index + 1}: {q.question}</h4>
            <div className="quiz-options">
              {q.options.map((option, optIndex) => (
                <label key={optIndex} className="quiz-option">
                  <input
                    type="radio"
                    name={`question-${index}`}
                    value={optIndex}
                    onChange={(e) => setQuizAnswers({
                      ...quizAnswers,
                      [index]: parseInt(e.target.value)
                    })}
                  />
                  {option}
                </label>
              ))}
            </div>
          </div>
        ))}
        <button className="quiz-submit">Submit Quiz</button>
      </div>
    );
  };

  const renderCalculator = () => {
    return (
      <div className="calculator-container">
        <h3>🧮 Interactive Calculator</h3>
        <div className="calculator-tools">
          <div className="calc-tool">
            <h4>ROI Calculator</h4>
            <div className="calc-inputs">
              <input
                type="number"
                placeholder="Initial Investment"
                onChange={(e) => setCalculatorValues({
                  ...calculatorValues,
                  investment: e.target.value
                })}
              />
              <input
                type="number"
                placeholder="Expected Return"
                onChange={(e) => setCalculatorValues({
                  ...calculatorValues,
                  return: e.target.value
                })}
              />
              <button>Calculate ROI</button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTemplates = () => {
    return (
      <div className="templates-container">
        <h3>📋 Ready-to-Use Templates</h3>
        <div className="template-list">
          <div className="template-item">
            <h4>Implementation Checklist</h4>
            <div className="template-preview">
              <p>✅ Step 1: Planning phase</p>
              <p>✅ Step 2: Resource allocation</p>
              <p>✅ Step 3: Execution</p>
              <p>✅ Step 4: Review and optimize</p>
            </div>
            <button>Download Template</button>
          </div>
        </div>
      </div>
    );
  };

  const renderWorkshop = () => {
    return (
      <div className="workshop-container">
        <h3>🛠️ Workshop Activities</h3>
        <div className="workshop-activities">
          <div className="activity">
            <h4>Group Exercise 1</h4>
            <p>Duration: 15 minutes</p>
            <p>Objective: Understanding core concepts</p>
            <button>Start Activity</button>
          </div>
        </div>
      </div>
    );
  };

  const renderTimeline = () => {
    return (
      <div className="timeline-container">
        <h3>📅 Implementation Timeline</h3>
        <div className="timeline">
          <div className="timeline-item">
            <div className="timeline-marker">1</div>
            <div className="timeline-content">
              <h4>Week 1-2: Planning</h4>
              <p>Define objectives and gather resources</p>
            </div>
          </div>
          <div className="timeline-item">
            <div className="timeline-marker">2</div>
            <div className="timeline-content">
              <h4>Week 3-4: Implementation</h4>
              <p>Execute the planned strategy</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderGameElements = () => {
    return (
      <div className="game-elements">
        <div className="progress-bar">
          <div className="progress-fill" style={{ width: '60%' }}></div>
        </div>
        <div className="achievements">
          <span className="achievement">🏆 Knowledge Explorer</span>
          <span className="achievement">📚 Quick Learner</span>
        </div>
      </div>
    );
  };

  const formatArticleContent = (content) => {
    // Same formatting logic as before
    if (!content) return null;
    const lines = content.split('\n');
    return lines.map((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('# ')) {
        return <h2 key={index}>{trimmedLine.substring(2)}</h2>;
      } else if (trimmedLine.startsWith('## ')) {
        return <h3 key={index}>{trimmedLine.substring(3)}</h3>;
      } else if (trimmedLine.length > 0) {
        return <p key={index}>{trimmedLine}</p>;
      }
      return null;
    });
  };

  return (
    <div className="enhanced-content-renderer">
      <div className="content-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>
      <div className="content-area">
        {renderContent()}
      </div>
    </div>
  );
}
