import { useEffect } from 'react';
import Breadcrumbs from './Breadcrumbs';

export default function ArticleView({ article, navigationPath, onBack, isLoading }) {
  useEffect(() => {
    // Text-to-speech functionality can be implemented here
  }, [article]);

  if (isLoading) {
    return <div className="loading-indicator">Generating article...</div>;
  }
  
  if (!article) return null;
  
  return (
    <div className="article-view">
      <Breadcrumbs path={navigationPath} onNavigate={onBack} />
      <h1>{article.titlu}</h1>
      <div className="article-content">
        {article.continut}
      </div>
      {article.flags && (
        <div className="article-flags">
          {article.flags.map((flag, index) => (
            <span key={index} className="flag">{flag}</span>
          ))}
        </div>
      )}
    </div>
  );
}