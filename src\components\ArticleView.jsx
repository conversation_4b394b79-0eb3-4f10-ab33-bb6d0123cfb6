import { useEffect, useState } from 'react';
import './ArticleView.css';

export default function ArticleView({ article, navigationPath, onBack, isLoading }) {
  const [isReading, setIsReading] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);

  useEffect(() => {
    // Calculate reading progress based on scroll
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setReadingProgress(Math.min(progress, 100));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleTextToSpeech = () => {
    if ('speechSynthesis' in window) {
      if (isReading) {
        speechSynthesis.cancel();
        setIsReading(false);
      } else {
        const utterance = new SpeechSynthesisUtterance(article.continut);
        utterance.onstart = () => setIsReading(true);
        utterance.onend = () => setIsReading(false);
        utterance.onerror = () => setIsReading(false);
        speechSynthesis.speak(utterance);
      }
    }
  };

  const handleSaveArticle = () => {
    // This will be handled by the parent component via gesture
    alert('Use S gesture to save this article');
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Generating article...</p>
      </div>
    );
  }

  if (!article) return null;

  return (
    <div className="article-view">
      {/* Progress bar */}
      <div className="reading-progress">
        <div
          className="progress-bar"
          style={{ width: `${readingProgress}%` }}
        ></div>
      </div>

      {/* Header */}
      <div className="article-header">
        <Breadcrumbs path={navigationPath} onNavigate={onBack} />
        <div className="article-actions">
          <button
            className={`tts-btn ${isReading ? 'active' : ''}`}
            onClick={handleTextToSpeech}
            title="Text-to-Speech (V gesture)"
          >
            {isReading ? '🔊' : '🔇'}
          </button>
          <button
            className="save-btn"
            onClick={handleSaveArticle}
            title="Save Article (S gesture)"
          >
            💾
          </button>
          <button
            className="back-btn"
            onClick={onBack}
            title="Go Back (← swipe)"
          >
            ←
          </button>
        </div>
      </div>

      {/* Article content */}
      <article className="article-content">
        <h1 className="article-title">{article.titlu}</h1>

        {article.pozitie && (
          <div className="article-position">
            <span className="position-text">{article.pozitie}</span>
          </div>
        )}

        <div className="article-body">
          {formatArticleContent(article.continut)}
        </div>

        {article.subcategorii && article.subcategorii.length > 0 && (
          <div className="related-topics">
            <h3>Related Topics</h3>
            <div className="topic-tags">
              {article.subcategorii.map((topic, index) => (
                <span key={index} className="topic-tag">{topic}</span>
              ))}
            </div>
          </div>
        )}

        {article.flags && article.flags.length > 0 && (
          <div className="article-flags">
            <h4>Article Flags:</h4>
            <div className="flags-list">
              {article.flags.map((flag, index) => (
                <span key={index} className="flag-badge">{flag}</span>
              ))}
            </div>
          </div>
        )}
      </article>

      {/* Gesture hints */}
      <div className="gesture-hints">
        <div className="hint">
          <span className="gesture">V</span>
          <span>Text-to-speech</span>
        </div>
        <div className="hint">
          <span className="gesture">S</span>
          <span>Save article</span>
        </div>
        <div className="hint">
          <span className="gesture">←</span>
          <span>Go back</span>
        </div>
      </div>
    </div>
  );
}

// Breadcrumbs component
function Breadcrumbs({ path, onNavigate }) {
  if (!path || path.length === 0) return null;

  return (
    <nav className="breadcrumbs">
      {path.map((item, index) => (
        <span key={index} className="breadcrumb-item">
          {index > 0 && <span className="separator">→</span>}
          <button
            className="breadcrumb-link"
            onClick={() => onNavigate(index)}
          >
            {item}
          </button>
        </span>
      ))}
    </nav>
  );
}

// Format article content with proper HTML structure
function formatArticleContent(content) {
  if (!content) return null;

  // Split content by lines and format as HTML
  const lines = content.split('\n');
  const formattedContent = [];

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();

    if (trimmedLine.startsWith('# ')) {
      formattedContent.push(
        <h2 key={index} className="content-heading-1">
          {trimmedLine.substring(2)}
        </h2>
      );
    } else if (trimmedLine.startsWith('## ')) {
      formattedContent.push(
        <h3 key={index} className="content-heading-2">
          {trimmedLine.substring(3)}
        </h3>
      );
    } else if (trimmedLine.startsWith('### ')) {
      formattedContent.push(
        <h4 key={index} className="content-heading-3">
          {trimmedLine.substring(4)}
        </h4>
      );
    } else if (trimmedLine.length > 0) {
      formattedContent.push(
        <p key={index} className="content-paragraph">
          {trimmedLine}
        </p>
      );
    }
  });

  return formattedContent;
}