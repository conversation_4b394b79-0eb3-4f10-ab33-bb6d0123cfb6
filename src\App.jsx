import { useState, useEffect } from 'react';
import TreeView from './components/TreeView';
import ArticleView from './components/ArticleView';
import GestureHandler from './components/GestureHandler';
import { generateKnowledgeTree, generateArticle } from './services/openRouterService';
import { saveArticle, getSavedArticles, saveNavigationHistory, getNavigationHistory } from './services/storageService';
import './App.css';

export default function App() {
  const [currentTopic, setCurrentTopic] = useState(null);
  const [currentTree, setCurrentTree] = useState(null);
  const [currentArticle, setCurrentArticle] = useState(null);
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [view, setView] = useState('input'); // 'input', 'tree', 'article'
  const [selectedBranch, setSelectedBranch] = useState(null);

  // Load navigation history on mount
  useEffect(() => {
    const history = getNavigationHistory();
    setNavigationHistory(history);
  }, []);

  // Handle topic input and tree generation
  const handleTopicSubmit = async (topic) => {
    if (!topic.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const tree = await generateKnowledgeTree(topic);
      setCurrentTopic(topic);
      setCurrentTree(tree);
      setView('tree');

      // Update navigation history
      const newHistory = [...navigationHistory, { topic, timestamp: Date.now() }];
      setNavigationHistory(newHistory);
      saveNavigationHistory(newHistory);
    } catch (err) {
      setError('Failed to generate knowledge tree. Please try again.');
      console.error('Error generating tree:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle branch selection and article generation
  const handleBranchSelect = async (branch, flags = ['-a']) => {
    setIsLoading(true);
    setError(null);
    setSelectedBranch(branch);

    try {
      const article = await generateArticle(currentTopic, branch, flags);
      setCurrentArticle(article);
      setView('article');
    } catch (err) {
      setError('Failed to generate article. Please try again.');
      console.error('Error generating article:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Gesture handlers
  const handleVerticalScroll = (direction) => {
    if (view === 'tree' && direction === 'down') {
      // Show branches (already visible)
    }
  };

  const handleReverseScroll = () => {
    if (view === 'tree' && selectedBranch) {
      handleBranchSelect(selectedBranch);
    }
  };

  const handleLateralScroll = () => {
    if (view === 'article') {
      setView('tree');
      setCurrentArticle(null);
    } else if (view === 'tree') {
      setView('input');
      setCurrentTree(null);
      setCurrentTopic(null);
    }
  };

  const handleVGesture = () => {
    if (currentArticle && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(currentArticle.continut);
      speechSynthesis.speak(utterance);
    }
  };

  const handleSGesture = () => {
    if (currentArticle) {
      const success = saveArticle(currentArticle);
      if (success) {
        alert('Article saved successfully!');
      } else {
        alert('Failed to save article.');
      }
    }
  };

  const handleTap = (target) => {
    // Handle tap gestures based on current view
    console.log('Tap detected:', target);
  };

  return (
    <div className="app">
      <GestureHandler
        onVerticalScroll={handleVerticalScroll}
        onReverseScroll={handleReverseScroll}
        onLateralScroll={handleLateralScroll}
        onVGesture={handleVGesture}
        onSGesture={handleSGesture}
        onTap={handleTap}
      >
        {error && (
          <div className="error-message">
            {error}
            <button onClick={() => setError(null)}>×</button>
          </div>
        )}

        {view === 'input' && (
          <div className="topic-input-container">
            <TopicInput onSubmit={handleTopicSubmit} isLoading={isLoading} />
          </div>
        )}

        {view === 'tree' && (
          <TreeView
            tree={currentTree}
            onBranchSelect={handleBranchSelect}
            isLoading={isLoading}
          />
        )}

        {view === 'article' && (
          <ArticleView
            article={currentArticle}
            navigationPath={[currentTopic, selectedBranch?.nume]}
            onBack={handleLateralScroll}
            isLoading={isLoading}
          />
        )}
      </GestureHandler>
    </div>
  );
}

// Topic Input Component
function TopicInput({ onSubmit, isLoading }) {
  const [topic, setTopic] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(topic);
  };

  return (
    <div className="topic-input">
      <h1>🌳 Knowledge Tree Explorer</h1>
      <p>Enter any topic to generate an interactive knowledge tree</p>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={topic}
          onChange={(e) => setTopic(e.target.value)}
          placeholder="e.g., Quantum Physics, Machine Learning, History of Art..."
          disabled={isLoading}
          className="topic-input-field"
        />
        <button type="submit" disabled={isLoading || !topic.trim()}>
          {isLoading ? 'Generating...' : 'Explore Knowledge 🚀'}
        </button>
      </form>
      <div className="gesture-hints">
        <p>📱 Gesture Controls:</p>
        <ul>
          <li>↓ Scroll down: Browse branches</li>
          <li>↑ Scroll up: Generate article</li>
          <li>← Swipe left: Go back</li>
          <li>V gesture: Text-to-speech</li>
          <li>S gesture: Save article</li>
        </ul>
      </div>
    </div>
  );
}