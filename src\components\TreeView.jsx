import { useState } from 'react';
import BranchItem from './BranchItem';

export default function TreeView({ tree, onBranchSelect, isLoading }) {
  if (isLoading) {
    return <div className="loading-indicator">Generating knowledge tree...</div>;
  }
  
  if (!tree) {
    return (
      <div className="topic-input">
        <h2>Enter a topic to explore</h2>
        <input type="text" placeholder="e.g., Quantum Physics" />
        <button>Generate Knowledge Tree</button>
      </div>
    );
  }
  
  return (
    <div className="tree-view">
      <h1>{tree.tema}</h1>
      <div className="branches-list">
        {tree.ramuri.map((branch, index) => (
          <BranchItem 
            key={index}
            branch={branch}
            onSelect={() => onBranchSelect(branch)}
          />
        ))}
      </div>
    </div>
  );
}