import { useState } from 'react';
import { FLAGS_CONFIG, FLAG_CATEGORIES, POPULAR_COMBINATIONS, estimateTotalTime, getComplexityLevel } from '../config/flagsConfig';
import './TreeView.css';

export default function TreeView({ tree, onBranchSelect, isLoading }) {
  const [selectedBranchIndex, setSelectedBranchIndex] = useState(null);
  const [showFlags, setShowFlags] = useState(false);
  const [selectedFlags, setSelectedFlags] = useState(['-a']);

  const availableFlags = FLAGS_CONFIG;

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Generating knowledge tree...</p>
      </div>
    );
  }

  if (!tree) {
    return (
      <div className="empty-state">
        <h2>No knowledge tree available</h2>
        <p>Please generate a tree first</p>
      </div>
    );
  }

  const handleBranchClick = (branch, index) => {
    if (selectedBranchIndex === index) {
      // If same branch clicked, show flag selection
      setShowFlags(true);
    } else {
      // Select new branch
      setSelectedBranchIndex(index);
      setShowFlags(false);
    }
  };

  const handleFlagToggle = (flag) => {
    setSelectedFlags(prev => {
      if (prev.includes(flag)) {
        return prev.filter(f => f !== flag);
      } else {
        return [...prev, flag];
      }
    });
  };

  const handleGenerateArticle = () => {
    if (selectedBranchIndex !== null) {
      const branch = tree.ramuri[selectedBranchIndex];
      const flags = selectedFlags.length > 0 ? selectedFlags : ['-a'];
      onBranchSelect(branch, flags);
    }
  };

  const renderFlagsByCategory = () => {
    return Object.entries(FLAG_CATEGORIES).map(([categoryKey, categoryInfo]) => {
      const categoryFlags = availableFlags.filter(flag => flag.category === categoryKey);

      return (
        <div key={categoryKey} className="flag-category">
          <h5 className="category-title">
            <span className="category-icon">{categoryInfo.icon}</span>
            {categoryInfo.name}
            <span className="category-count">({categoryFlags.length})</span>
          </h5>
          <div className="flags-grid">
            {categoryFlags.map(({ flag, label, description, complexity, estimatedTime }) => (
              <div
                key={flag}
                className={`flag-option ${selectedFlags.includes(flag) ? 'selected' : ''} complexity-${complexity}`}
                onClick={() => handleFlagToggle(flag)}
              >
                <div className="flag-header">
                  <div className="flag-label">{flag}</div>
                  {estimatedTime && <div className="flag-time">⏱️ {estimatedTime}</div>}
                </div>
                <div className="flag-name">{label}</div>
                <div className="flag-desc">{description}</div>
                {complexity && (
                  <div className={`flag-complexity complexity-${complexity}`}>
                    {complexity.charAt(0).toUpperCase() + complexity.slice(1)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="tree-view">
      <div className="tree-header">
        <h1>{tree.tema}</h1>
        <p className="tree-subtitle">Explore the knowledge branches below</p>
      </div>

      <div className="branches-container">
        {tree.ramuri.map((branch, index) => (
          <BranchItem
            key={index}
            branch={branch}
            index={index}
            isSelected={selectedBranchIndex === index}
            onClick={() => handleBranchClick(branch, index)}
          />
        ))}
      </div>

      {selectedBranchIndex !== null && (
        <div className="branch-details">
          <div className="selected-branch-info">
            <h3>
              {tree.ramuri[selectedBranchIndex].emoji} {tree.ramuri[selectedBranchIndex].nume}
            </h3>
            <p>{tree.ramuri[selectedBranchIndex].descriere}</p>

            {tree.ramuri[selectedBranchIndex].subcategorii && (
              <div className="subcategories">
                <h4>Subcategories:</h4>
                <div className="subcategory-tags">
                  {tree.ramuri[selectedBranchIndex].subcategorii.map((sub, idx) => (
                    <span key={idx} className="subcategory-tag">{sub}</span>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="action-buttons">
            <button
              className="generate-btn primary"
              onClick={handleGenerateArticle}
            >
              Generate Article 📄
            </button>
            <button
              className="flags-btn secondary"
              onClick={() => setShowFlags(!showFlags)}
            >
              Customize Flags ⚙️
            </button>
          </div>

          {showFlags && (
            <div className="flags-panel">
              <h4>Article Flags (stackable):</h4>
              <div className="flags-categories">
                {renderFlagsByCategory()}
              </div>
              <div className="selected-flags">
                <div className="flags-summary">
                  <strong>Selected ({selectedFlags.length}): </strong>
                  {selectedFlags.join(' ')}
                  {selectedFlags.length === 0 && <span className="default-flag">-a (default)</span>}
                </div>
                <div className="flags-metadata">
                  <span className="estimated-time">
                    ⏱️ Estimated time: {estimateTotalTime(selectedFlags)}
                  </span>
                  <span className="complexity-level">
                    📊 Complexity: {getComplexityLevel(selectedFlags)}
                  </span>
                </div>
              </div>

              <div className="popular-combinations">
                <h6>Quick Select:</h6>
                <div className="combo-buttons">
                  {Object.entries(POPULAR_COMBINATIONS).map(([key, combo]) => (
                    <button
                      key={key}
                      className="combo-btn"
                      onClick={() => setSelectedFlags(combo.flags)}
                      title={combo.description}
                    >
                      {combo.name}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flag-actions">
                <button
                  className="clear-flags-btn"
                  onClick={() => setSelectedFlags(['-a'])}
                >
                  Reset to Default
                </button>
                <button
                  className="select-all-btn"
                  onClick={() => setSelectedFlags(availableFlags.slice(0, 5).map(f => f.flag))}
                >
                  Top 5 Popular
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="gesture-hints">
        <div className="hint">
          <span className="gesture">↑</span>
          <span>Scroll up to generate article</span>
        </div>
        <div className="hint">
          <span className="gesture">←</span>
          <span>Swipe left to go back</span>
        </div>
        <div className="hint">
          <span className="gesture">👆</span>
          <span>Tap to select branch</span>
        </div>
      </div>
    </div>
  );
}

function BranchItem({ branch, isSelected, onClick }) {
  return (
    <div
      className={`branch-item ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="branch-emoji">{branch.emoji}</div>
      <div className="branch-content">
        <h3 className="branch-name">{branch.nume}</h3>
        <p className="branch-description">{branch.descriere}</p>
        {branch.subcategorii && branch.subcategorii.length > 0 && (
          <div className="branch-subcategories">
            {branch.subcategorii.slice(0, 3).map((sub, idx) => (
              <span key={idx} className="subcategory-preview">{sub}</span>
            ))}
            {branch.subcategorii.length > 3 && (
              <span className="subcategory-more">+{branch.subcategorii.length - 3} more</span>
            )}
          </div>
        )}
      </div>
      <div className="branch-arrow">
        {isSelected ? '▼' : '▶'}
      </div>
    </div>
  );
}